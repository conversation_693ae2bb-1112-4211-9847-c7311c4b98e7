# Source files
src/
tests/
examples/
docs/

# Configuration files
.eslintrc.js
.prettierrc
jest.config.js
tsconfig.json
pnpm-workspace.yaml

# Development files
.github/
.vscode/
.idea/

# Test files
coverage/
*.test.ts
*.spec.ts
__tests__/

# Documentation source
*.md
!README.md

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# Temporary folders
tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~
