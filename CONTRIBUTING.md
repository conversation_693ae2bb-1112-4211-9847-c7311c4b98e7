# Contributing to @roasmax/rabbitmq

Thank you for your interest in contributing to @roasmax/rabbitmq! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Making Changes](#making-changes)
- [Testing](#testing)
- [Submitting Changes](#submitting-changes)
- [Code Style](#code-style)
- [Documentation](#documentation)

## Code of Conduct

This project adheres to a code of conduct. By participating, you are expected to uphold this code. Please report unacceptable behavior to the project maintainers.

## Getting Started

1. Fork the repository on GitHub
2. Clone your fork locally
3. Create a new branch for your feature or bug fix
4. Make your changes
5. Test your changes
6. Submit a pull request

## Development Setup

### Prerequisites

- Node.js 16.x or higher
- pnpm 8.x or higher
- RabbitMQ server (for integration tests)

### Installation

```bash
# Clone your fork
git clone https://github.com/your-username/rabbitmq.git
cd rabbitmq

# Install dependencies
pnpm install

# Build the project
pnpm build

# Run tests
pnpm test
```

### Running RabbitMQ for Development

You can use Docker to run RabbitMQ locally:

```bash
docker run -d --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=guest \
  -e RABBITMQ_DEFAULT_PASS=guest \
  rabbitmq:3.12-management
```

## Making Changes

### Branch Naming

Use descriptive branch names:
- `feature/add-new-pattern` for new features
- `fix/connection-pool-leak` for bug fixes
- `docs/update-readme` for documentation updates
- `refactor/improve-error-handling` for refactoring

### Commit Messages

Follow conventional commit format:
- `feat: add support for message compression`
- `fix: resolve connection pool memory leak`
- `docs: update API documentation`
- `test: add integration tests for RPC pattern`
- `refactor: improve error handling in consumers`

## Testing

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run tests in watch mode
pnpm test:watch

# Run specific test file
pnpm test src/core/message.test.ts
```

### Writing Tests

- Write unit tests for all new functionality
- Include integration tests for complex features
- Aim for high test coverage (>80%)
- Use descriptive test names
- Follow the existing test patterns

### Test Structure

```typescript
describe('FeatureName', () => {
  describe('methodName', () => {
    it('should do something specific', () => {
      // Arrange
      const input = 'test input';
      
      // Act
      const result = methodUnderTest(input);
      
      // Assert
      expect(result).toBe('expected output');
    });
  });
});
```

## Submitting Changes

### Pull Request Process

1. Ensure your code follows the project's coding standards
2. Update documentation if needed
3. Add or update tests for your changes
4. Ensure all tests pass
5. Update CHANGELOG.md with your changes
6. Submit a pull request with a clear description

### Pull Request Template

```markdown
## Description
Brief description of the changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] All tests pass

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
```

## Code Style

### TypeScript Guidelines

- Use TypeScript strict mode
- Prefer explicit types over `any`
- Use meaningful variable and function names
- Follow existing naming conventions
- Add JSDoc comments for public APIs

### Formatting

The project uses Prettier and ESLint for code formatting:

```bash
# Format code
pnpm format

# Lint code
pnpm lint

# Fix linting issues
pnpm lint:fix
```

### Code Organization

- Keep files focused and cohesive
- Use barrel exports (index.ts files)
- Organize imports: external libraries first, then internal modules
- Use consistent file naming (kebab-case for files, PascalCase for classes)

## Documentation

### API Documentation

- Use JSDoc comments for all public APIs
- Include examples in documentation
- Document parameters, return values, and exceptions
- Keep documentation up to date with code changes

### README Updates

- Update README.md for significant changes
- Include usage examples
- Update installation instructions if needed
- Keep the feature list current

### Examples

- Add examples for new features
- Update existing examples when APIs change
- Ensure examples are tested and working
- Include both basic and advanced usage examples

## Release Process

Releases are handled by maintainers:

1. Update version in package.json
2. Update CHANGELOG.md
3. Create and push a git tag
4. GitHub Actions will automatically publish to npm

## Getting Help

- Open an issue for bugs or feature requests
- Start a discussion for questions or ideas
- Check existing issues and discussions first
- Be respectful and constructive in all interactions

## Recognition

Contributors will be recognized in:
- CHANGELOG.md for their contributions
- GitHub contributors list
- Special recognition for significant contributions

Thank you for contributing to @roasmax/rabbitmq!
