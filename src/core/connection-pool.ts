import { connect } from 'amqplib';
import { EventEmitter } from 'events';
import { RabbitMQConfig, ConnectionPoolConfig, ManagedConnection, ConnectionError, Logger } from '@/types';
import { createLogger } from '@/utils/logger';

interface PooledConnection extends ManagedConnection {
  id: string;
  createdAt: number;
  usageCount: number;
}

export class ConnectionPool extends EventEmitter {
  private readonly config: RabbitMQConfig;
  private readonly poolConfig: ConnectionPoolConfig;
  private readonly logger: Logger;
  private readonly connections: Map<string, PooledConnection> = new Map();
  private readonly availableConnections: string[] = [];
  private readonly busyConnections: Set<string> = new Set();
  private isShuttingDown = false;
  private reapTimer?: NodeJS.Timeout;

  constructor(
    config: RabbitMQConfig,
    poolConfig: Partial<ConnectionPoolConfig> = {},
    logger?: Logger
  ) {
    super();
    this.config = config;
    this.poolConfig = {
      poolSize: 10,
      acquireTimeout: 30000,
      idleTimeout: 300000, // 5 minutes
      reapInterval: 60000, // 1 minute
      ...poolConfig,
    };
    this.logger = logger ?? createLogger('ConnectionPool');
    this.startReaper();
  }

  public async getConnection(): Promise<ManagedConnection> {
    if (this.isShuttingDown) {
      throw new ConnectionError('Connection pool is shutting down');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new ConnectionError('Connection acquisition timeout'));
      }, this.poolConfig.acquireTimeout);

      this.acquireConnection()
        .then(connection => {
          clearTimeout(timeout);
          resolve(connection);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  public async returnConnection(connection: ManagedConnection): Promise<void> {
    const pooledConnection = Array.from(this.connections.values()).find(
      conn => conn.connection === connection.connection
    );

    if (!pooledConnection) {
      this.logger.warn('Attempted to return unknown connection');
      return;
    }

    if (!pooledConnection.isActive || (pooledConnection.connection as any).connection?.destroyed) {
      await this.removeConnection(pooledConnection.id);
      return;
    }

    this.busyConnections.delete(pooledConnection.id);
    this.availableConnections.push(pooledConnection.id);
    pooledConnection.lastUsed = Date.now();

    this.logger.debug(`Connection ${pooledConnection.id} returned to pool`);
    this.emit('connectionReturned', pooledConnection);
  }

  private async acquireConnection(): Promise<ManagedConnection> {
    // Try to get an available connection
    let connectionId = this.availableConnections.pop();
    
    if (connectionId) {
      const connection = this.connections.get(connectionId);
      if (connection && connection.isActive) {
        this.busyConnections.add(connectionId);
        connection.usageCount++;
        this.logger.debug(`Reusing connection ${connectionId}`);
        return connection;
      } else if (connection) {
        await this.removeConnection(connectionId);
      }
    }

    // Create new connection if pool is not full
    if (this.connections.size < this.poolConfig.poolSize) {
      return await this.createConnection();
    }

    // Wait for a connection to become available
    return new Promise((resolve, reject) => {
      const onConnectionReturned = (connection: PooledConnection) => {
        if (this.availableConnections.length > 0) {
          this.removeListener('connectionReturned', onConnectionReturned);
          this.acquireConnection().then(resolve).catch(reject);
        }
      };

      this.on('connectionReturned', onConnectionReturned);
    });
  }

  private async createConnection(): Promise<PooledConnection> {
    const connectionId = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      this.logger.debug(`Creating new connection ${connectionId}`);
      
      const connection = await connect(this.buildConnectionUrl(), this.buildConnectionOptions());
      const channel = await connection.createChannel();

      const pooledConnection: PooledConnection = {
        id: connectionId,
        connection: connection as any,
        channel,
        isActive: true,
        lastUsed: Date.now(),
        createdAt: Date.now(),
        usageCount: 1,
      };

      // Set up connection event handlers
      connection.on('error', (error: Error) => {
        this.logger.error(`Connection ${connectionId} error`, error);
        this.handleConnectionError(connectionId, error);
      });

      connection.on('close', () => {
        this.logger.info(`Connection ${connectionId} closed`);
        this.removeConnection(connectionId).catch(error => {
          this.logger.error(`Error removing closed connection ${connectionId}`, error);
        });
      });

      channel.on('error', (error: Error) => {
        this.logger.error(`Channel error on connection ${connectionId}`, error);
        this.handleConnectionError(connectionId, error);
      });

      channel.on('close', () => {
        this.logger.info(`Channel closed on connection ${connectionId}`);
        this.handleConnectionError(connectionId, new Error('Channel closed'));
      });

      this.connections.set(connectionId, pooledConnection);
      this.busyConnections.add(connectionId);

      this.logger.info(`Created connection ${connectionId}`);
      this.emit('connectionCreated', pooledConnection);

      return pooledConnection;
    } catch (error) {
      this.logger.error(`Failed to create connection ${connectionId}`, error as Error);
      throw new ConnectionError(`Failed to create connection: ${error}`);
    }
  }

  private async removeConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    connection.isActive = false;
    this.connections.delete(connectionId);
    this.busyConnections.delete(connectionId);
    
    const availableIndex = this.availableConnections.indexOf(connectionId);
    if (availableIndex !== -1) {
      this.availableConnections.splice(availableIndex, 1);
    }

    try {
      if (connection.channel && !(connection.channel as any).connection?.destroyed) {
        await connection.channel.close();
      }
      if (connection.connection && !(connection.connection as any).connection?.destroyed) {
        await (connection.connection as any).close();
      }
    } catch (error) {
      this.logger.warn(`Error closing connection ${connectionId}`, { error });
    }

    this.logger.info(`Removed connection ${connectionId}`);
    this.emit('connectionRemoved', connection);
  }

  private handleConnectionError(connectionId: string, error: Error): void {
    this.removeConnection(connectionId).catch(removeError => {
      this.logger.error(`Error removing failed connection ${connectionId}`, removeError);
    });
    this.emit('error', error);
  }

  private buildConnectionUrl(): string {
    const { host, port, username, password, virtualHost } = this.config;
    const encodedVirtualHost = encodeURIComponent(virtualHost);
    const protocol = this.config.sslEnabled ? 'amqps' : 'amqp';
    return `${protocol}://${username}:${password}@${host}:${port}/${encodedVirtualHost}`;
  }

  private buildConnectionOptions(): Record<string, unknown> {
    const options: Record<string, unknown> = {
      heartbeat: this.config.heartbeat,
      timeout: this.config.connectionTimeout,
    };

    if (this.config.sslEnabled && this.config.sslOptions) {
      (options as any).ssl = this.config.sslOptions;
    }

    return options;
  }

  private startReaper(): void {
    this.reapTimer = setInterval(() => {
      this.reapIdleConnections().catch(error => {
        this.logger.error('Error during connection reaping', error);
      });
    }, this.poolConfig.reapInterval);
  }

  private async reapIdleConnections(): Promise<void> {
    const now = Date.now();
    const connectionsToReap: string[] = [];

    for (const [connectionId, connection] of this.connections) {
      if (
        !this.busyConnections.has(connectionId) &&
        now - connection.lastUsed > this.poolConfig.idleTimeout
      ) {
        connectionsToReap.push(connectionId);
      }
    }

    for (const connectionId of connectionsToReap) {
      this.logger.debug(`Reaping idle connection ${connectionId}`);
      await this.removeConnection(connectionId);
    }
  }

  public async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    
    if (this.reapTimer) {
      clearInterval(this.reapTimer);
    }

    const connectionIds = Array.from(this.connections.keys());
    await Promise.all(connectionIds.map(id => this.removeConnection(id)));

    this.logger.info('Connection pool shut down');
    this.emit('shutdown');
  }

  public getStats(): {
    totalConnections: number;
    availableConnections: number;
    busyConnections: number;
    poolSize: number;
  } {
    return {
      totalConnections: this.connections.size,
      availableConnections: this.availableConnections.length,
      busyConnections: this.busyConnections.size,
      poolSize: this.poolConfig.poolSize,
    };
  }
}
