import { ConfigManager, validateConfig, isValidConfig } from '../../src/core/config';
import { RabbitMQConfig } from '../../src/types';

describe('ConfigManager', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('constructor', () => {
    it('should create config with defaults', () => {
      const config = new ConfigManager();
      const rabbitConfig = config.getRabbitMQConfig();

      expect(rabbitConfig.host).toBe('localhost');
      expect(rabbitConfig.port).toBe(5672);
      expect(rabbitConfig.username).toBe('guest');
      expect(rabbitConfig.password).toBe('guest');
      expect(rabbitConfig.virtualHost).toBe('/');
    });

    it('should load config from environment variables', () => {
      process.env.RABBITMQ_HOST = 'test-host';
      process.env.RABBITMQ_PORT = '1234';
      process.env.RABBITMQ_USERNAME = 'test-user';
      process.env.RABBITMQ_PASSWORD = 'test-pass';
      process.env.RABBITMQ_VIRTUAL_HOST = '/test';

      const config = new ConfigManager();
      const rabbitConfig = config.getRabbitMQConfig();

      expect(rabbitConfig.host).toBe('test-host');
      expect(rabbitConfig.port).toBe(1234);
      expect(rabbitConfig.username).toBe('test-user');
      expect(rabbitConfig.password).toBe('test-pass');
      expect(rabbitConfig.virtualHost).toBe('/test');
    });

    it('should use custom environment prefix', () => {
      process.env.CUSTOM_HOST = 'custom-host';
      process.env.CUSTOM_PORT = '9999';

      const config = new ConfigManager(undefined, 'CUSTOM_');
      const rabbitConfig = config.getRabbitMQConfig();

      expect(rabbitConfig.host).toBe('custom-host');
      expect(rabbitConfig.port).toBe(9999);
    });
  });

  describe('updateConfig', () => {
    it('should update configuration', () => {
      const config = new ConfigManager();
      
      config.updateConfig({
        host: 'updated-host',
        port: 5673,
      });

      const rabbitConfig = config.getRabbitMQConfig();
      expect(rabbitConfig.host).toBe('updated-host');
      expect(rabbitConfig.port).toBe(5673);
    });

    it('should validate configuration updates', () => {
      const config = new ConfigManager();

      expect(() => {
        config.updateConfig({
          port: -1, // Invalid port
        });
      }).toThrow();
    });
  });

  describe('getConnectionUrl', () => {
    it('should generate correct connection URL', () => {
      const config = new ConfigManager();
      config.updateConfig({
        host: 'test-host',
        port: 5672,
        username: 'test-user',
        password: 'test-pass',
        virtualHost: '/test',
      });

      const url = config.getConnectionUrl();
      expect(url).toBe('amqp://test-user:test-pass@test-host:5672/test');
    });

    it('should encode virtual host', () => {
      const config = new ConfigManager();
      config.updateConfig({
        virtualHost: '/test with spaces',
      });

      const url = config.getConnectionUrl();
      expect(url).toContain('/test%20with%20spaces');
    });
  });

  describe('getConnectionOptions', () => {
    it('should return connection options', () => {
      const config = new ConfigManager();
      config.updateConfig({
        heartbeat: 300,
        connectionTimeout: 15000,
        sslEnabled: false,
      });

      const options = config.getConnectionOptions();
      expect(options.heartbeat).toBe(300);
      expect(options.timeout).toBe(15000);
      expect(options.protocol).toBeUndefined();
    });

    it('should include SSL options when enabled', () => {
      const config = new ConfigManager();
      config.updateConfig({
        sslEnabled: true,
        sslOptions: { rejectUnauthorized: false },
      });

      const options = config.getConnectionOptions();
      expect(options.protocol).toBe('amqps');
      expect(options.ssl).toEqual({ rejectUnauthorized: false });
    });
  });
});

describe('validateConfig', () => {
  it('should validate valid config', () => {
    const validConfig: RabbitMQConfig = {
      host: 'localhost',
      port: 5672,
      username: 'guest',
      password: 'guest',
      virtualHost: '/',
      heartbeat: 600,
      connectionTimeout: 30000,
      sslEnabled: false,
      sslOptions: {},
      maxRetries: 3,
      retryDelay: 1000,
    };

    expect(() => validateConfig(validConfig)).not.toThrow();
  });

  it('should reject invalid config', () => {
    const invalidConfig = {
      host: 'localhost',
      port: -1, // Invalid port
      username: 'guest',
      password: 'guest',
    };

    expect(() => validateConfig(invalidConfig)).toThrow();
  });
});

describe('isValidConfig', () => {
  it('should return true for valid config', () => {
    const validConfig: RabbitMQConfig = {
      host: 'localhost',
      port: 5672,
      username: 'guest',
      password: 'guest',
      virtualHost: '/',
      heartbeat: 600,
      connectionTimeout: 30000,
      sslEnabled: false,
      sslOptions: {},
      maxRetries: 3,
      retryDelay: 1000,
    };

    expect(isValidConfig(validConfig)).toBe(true);
  });

  it('should return false for invalid config', () => {
    const invalidConfig = {
      host: 'localhost',
      port: -1, // Invalid port
    };

    expect(isValidConfig(invalidConfig)).toBe(false);
  });
});
