import { Message, TypedMessage, MessageProcessResult, MessageBuilder, createMessage } from '../../src/core/message';

describe('Message', () => {
  describe('constructor', () => {
    it('should create message with body', () => {
      const body = { test: 'data' };
      const message = new Message(body);

      expect(message.body).toEqual(body);
      expect(message.id).toBeDefined();
      expect(message.timestamp).toBeDefined();
      expect(message.retryCount).toBe(0);
      expect(message.headers).toEqual({});
      expect(message.properties).toEqual({});
    });

    it('should create message with headers and properties', () => {
      const body = { test: 'data' };
      const headers = { 'x-custom': 'header' };
      const properties = { priority: 5 };
      const message = new Message(body, headers, properties);

      expect(message.headers).toEqual(headers);
      expect(message.properties).toEqual(properties);
    });

    it('should create message with custom ID', () => {
      const customId = 'custom-id-123';
      const message = new Message('test', {}, {}, customId);

      expect(message.id).toBe(customId);
    });
  });

  describe('header methods', () => {
    it('should add header', () => {
      const message = new Message('test');
      message.addHeader('test-header', 'test-value');

      expect(message.getHeader('test-header')).toBe('test-value');
    });

    it('should add multiple headers', () => {
      const message = new Message('test');
      const headers = { header1: 'value1', header2: 'value2' };
      message.addHeaders(headers);

      expect(message.getHeader('header1')).toBe('value1');
      expect(message.getHeader('header2')).toBe('value2');
    });
  });

  describe('property methods', () => {
    it('should add property', () => {
      const message = new Message('test');
      message.addProperty('test-prop', 'test-value');

      expect(message.getProperty('test-prop')).toBe('test-value');
    });

    it('should add multiple properties', () => {
      const message = new Message('test');
      const properties = { prop1: 'value1', prop2: 'value2' };
      message.addProperties(properties);

      expect(message.getProperty('prop1')).toBe('value1');
      expect(message.getProperty('prop2')).toBe('value2');
    });
  });

  describe('retry methods', () => {
    it('should increment retry count', () => {
      const message = new Message('test');
      expect(message.retryCount).toBe(0);

      message.incrementRetry();
      expect(message.retryCount).toBe(1);

      message.incrementRetry();
      expect(message.retryCount).toBe(2);
    });
  });

  describe('expiration methods', () => {
    it('should check if message is expired', () => {
      const message = new Message('test');
      
      expect(message.isExpired(1000)).toBe(false);
      expect(message.isExpired()).toBe(false);
    });

    it('should get message age', () => {
      const message = new Message('test');
      
      // Age should be very small (just created)
      expect(message.getAge()).toBeGreaterThanOrEqual(0);
      expect(message.getAge()).toBeLessThan(100);
    });
  });

  describe('serialization methods', () => {
    it('should convert to object', () => {
      const body = { test: 'data' };
      const headers = { 'x-custom': 'header' };
      const properties = { priority: 5 };
      const message = new Message(body, headers, properties);

      const obj = message.toObject();

      expect(obj.body).toEqual(body);
      expect(obj.headers).toEqual(headers);
      expect(obj.properties).toEqual(properties);
      expect(obj.id).toBe(message.id);
      expect(obj.timestamp).toBe(message.timestamp);
      expect(obj.retryCount).toBe(0);
    });

    it('should create from object', () => {
      const messageData = {
        id: 'test-id',
        body: { test: 'data' },
        headers: { 'x-custom': 'header' },
        properties: { priority: 5 },
        timestamp: Date.now(),
        retryCount: 2,
      };

      const message = Message.fromObject(messageData);

      expect(message.id).toBe(messageData.id);
      expect(message.body).toEqual(messageData.body);
      expect(message.headers).toEqual(messageData.headers);
      expect(message.properties).toEqual(messageData.properties);
      expect(message.timestamp).toBe(messageData.timestamp);
      expect(message.retryCount).toBe(messageData.retryCount);
    });
  });

  describe('clone method', () => {
    it('should clone message', () => {
      const original = new Message({ test: 'data' });
      original.addHeader('test-header', 'test-value');
      original.addProperty('test-prop', 'test-value');
      original.incrementRetry();

      const cloned = original.clone();

      expect(cloned.id).toBe(original.id);
      expect(cloned.body).toEqual(original.body);
      expect(cloned.headers).toEqual(original.headers);
      expect(cloned.properties).toEqual(original.properties);
      expect(cloned.timestamp).toBe(original.timestamp);
      expect(cloned.retryCount).toBe(original.retryCount);

      // Should be different objects
      expect(cloned).not.toBe(original);
      expect(cloned.headers).not.toBe(original.headers);
      expect(cloned.properties).not.toBe(original.properties);
    });
  });

  describe('typed method', () => {
    it('should create typed message', () => {
      interface TestData {
        name: string;
        value: number;
      }

      const data: TestData = { name: 'test', value: 42 };
      const typedMessage = Message.typed(data);

      expect(typedMessage).toBeInstanceOf(TypedMessage);
      expect(typedMessage.body).toEqual(data);
    });
  });
});

describe('TypedMessage', () => {
  it('should create typed message', () => {
    interface TestData {
      name: string;
      value: number;
    }

    const data: TestData = { name: 'test', value: 42 };
    const message = new TypedMessage(data);

    expect(message.body).toEqual(data);
    expect(message.body.name).toBe('test');
    expect(message.body.value).toBe(42);
  });

  it('should clone typed message', () => {
    interface TestData {
      name: string;
      value: number;
    }

    const data: TestData = { name: 'test', value: 42 };
    const original = new TypedMessage(data);
    const cloned = original.clone();

    expect(cloned).toBeInstanceOf(TypedMessage);
    expect(cloned.body).toEqual(original.body);
    expect(cloned).not.toBe(original);
  });
});

describe('MessageProcessResult', () => {
  describe('constructor', () => {
    it('should create result with all parameters', () => {
      const result = new MessageProcessResult(true, 'Success', { data: 'test' }, false);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Success');
      expect(result.data).toEqual({ data: 'test' });
      expect(result.shouldRetry).toBe(false);
    });
  });

  describe('static factory methods', () => {
    it('should create success result', () => {
      const result = MessageProcessResult.success({ data: 'test' }, 'Custom success');

      expect(result.success).toBe(true);
      expect(result.message).toBe('Custom success');
      expect(result.data).toEqual({ data: 'test' });
      expect(result.shouldRetry).toBe(false);
    });

    it('should create failure result', () => {
      const result = MessageProcessResult.failure('Custom failure', true);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Custom failure');
      expect(result.data).toBeUndefined();
      expect(result.shouldRetry).toBe(true);
    });

    it('should create retry result', () => {
      const result = MessageProcessResult.retry('Custom retry');

      expect(result.success).toBe(false);
      expect(result.message).toBe('Custom retry');
      expect(result.data).toBeUndefined();
      expect(result.shouldRetry).toBe(true);
    });

    it('should create result from error', () => {
      const error = new Error('Test error');
      const result = MessageProcessResult.fromError(error, true);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Test error');
      expect(result.data).toBeUndefined();
      expect(result.shouldRetry).toBe(true);
    });
  });

  describe('toObject method', () => {
    it('should convert to object', () => {
      const result = new MessageProcessResult(true, 'Success', { data: 'test' }, false);
      const obj = result.toObject();

      expect(obj.success).toBe(true);
      expect(obj.message).toBe('Success');
      expect(obj.data).toEqual({ data: 'test' });
      expect(obj.shouldRetry).toBe(false);
    });
  });
});

describe('MessageBuilder', () => {
  it('should build message with fluent interface', () => {
    const message = createMessage({ test: 'data' })
      .withId('custom-id')
      .withHeader('x-custom', 'header-value')
      .withProperty('priority', 5)
      .withPriority(10)
      .withExpiration(30000)
      .withCorrelationId('corr-123')
      .withReplyTo('reply-queue')
      .build();

    expect(message.id).toBe('custom-id');
    expect(message.body).toEqual({ test: 'data' });
    expect(message.getHeader('x-custom')).toBe('header-value');
    expect(message.getProperty('priority')).toBe(10); // Should be overridden by withPriority
    expect(message.getProperty('expiration')).toBe(30000);
    expect(message.getProperty('correlationId')).toBe('corr-123');
    expect(message.getProperty('replyTo')).toBe('reply-queue');
  });

  it('should build typed message', () => {
    interface TestData {
      name: string;
      value: number;
    }

    const data: TestData = { name: 'test', value: 42 };
    const message = createMessage(data)
      .withHeader('x-type', 'test')
      .buildTyped<TestData>();

    expect(message).toBeInstanceOf(TypedMessage);
    expect(message.body).toEqual(data);
    expect(message.body.name).toBe('test');
    expect(message.body.value).toBe(42);
    expect(message.getHeader('x-type')).toBe('test');
  });
});
