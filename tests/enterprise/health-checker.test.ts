import { <PERSON><PERSON>he<PERSON>, createHealth<PERSON>he<PERSON> } from '../../src/enterprise/health-checker';
import { ConnectionPool } from '../../src/core/connection-pool';
import { RabbitMQConfig } from '../../src/types';

// Mock ConnectionPool
jest.mock('../../src/core/connection-pool');

describe('HealthChecker', () => {
  let mockConnectionPool: jest.Mocked<ConnectionPool>;
  let healthChecker: HealthChecker;

  beforeEach(() => {
    mockConnectionPool = {
      getConnection: jest.fn(),
      returnConnection: jest.fn(),
      getStats: jest.fn(),
    } as any;

    healthChecker = new HealthChecker(mockConnectionPool, {
      memoryThreshold: 100, // 100MB for testing
    });
  });

  describe('checkHealth', () => {
    it('should return healthy status when all checks pass', async () => {
      // Mock successful connection
      const mockConnection = {
        channel: {
          checkQueue: jest.fn().mockResolvedValue({}),
        },
      };
      
      mockConnectionPool.getConnection.mockResolvedValue(mockConnection as any);
      mockConnectionPool.returnConnection.mockResolvedValue();

      const health = await healthChecker.checkHealth();

      expect(health.status).toBe('healthy');
      expect(health.checks.connection.status).toBe('pass');
      expect(health.checks.memory.status).toBe('pass');
      expect(health.timestamp).toBeDefined();
      expect(health.uptime).toBeGreaterThan(0);
    });

    it('should return unhealthy status when connection fails', async () => {
      mockConnectionPool.getConnection.mockRejectedValue(new Error('Connection failed'));

      const health = await healthChecker.checkHealth();

      expect(health.status).toBe('unhealthy');
      expect(health.checks.connection.status).toBe('fail');
      expect(health.checks.connection.message).toContain('Connection failed');
    });

    it('should detect high memory usage', async () => {
      // Mock successful connection
      const mockConnection = {
        channel: {
          checkQueue: jest.fn().mockResolvedValue({}),
        },
      };
      
      mockConnectionPool.getConnection.mockResolvedValue(mockConnection as any);
      mockConnectionPool.returnConnection.mockResolvedValue();

      // Create health checker with very low memory threshold
      const lowMemoryChecker = new HealthChecker(mockConnectionPool, {
        memoryThreshold: 1, // 1MB - will definitely be exceeded
      });

      const health = await lowMemoryChecker.checkHealth();

      expect(health.checks.memory.status).toBe('fail');
      expect(health.status).toBe('unhealthy');
    });

    it('should handle connection check errors gracefully', async () => {
      const mockConnection = {
        channel: {
          checkQueue: jest.fn().mockRejectedValue(new Error('Channel error')),
        },
      };
      
      mockConnectionPool.getConnection.mockResolvedValue(mockConnection as any);
      mockConnectionPool.returnConnection.mockResolvedValue();

      const health = await healthChecker.checkHealth();

      expect(health.checks.connection.status).toBe('fail');
      expect(health.checks.connection.message).toContain('Channel error');
    });
  });

  describe('getConnectionStats', () => {
    it('should return connection pool statistics', () => {
      const mockStats = {
        totalConnections: 5,
        availableConnections: 3,
        busyConnections: 2,
        poolSize: 10,
      };

      mockConnectionPool.getStats.mockReturnValue(mockStats);

      const stats = healthChecker.getConnectionStats();

      expect(stats).toEqual(mockStats);
      expect(mockConnectionPool.getStats).toHaveBeenCalled();
    });
  });

  describe('getSystemInfo', () => {
    it('should return system information', () => {
      const systemInfo = healthChecker.getSystemInfo();

      expect(systemInfo).toHaveProperty('nodeVersion');
      expect(systemInfo).toHaveProperty('platform');
      expect(systemInfo).toHaveProperty('arch');
      expect(systemInfo).toHaveProperty('uptime');
      expect(systemInfo).toHaveProperty('memory');
      expect(systemInfo).toHaveProperty('cpuUsage');

      expect(typeof systemInfo.nodeVersion).toBe('string');
      expect(typeof systemInfo.platform).toBe('string');
      expect(typeof systemInfo.arch).toBe('string');
      expect(typeof systemInfo.uptime).toBe('number');
      expect(typeof systemInfo.memory).toBe('object');
      expect(typeof systemInfo.cpuUsage).toBe('object');
    });
  });
});

describe('createHealthChecker', () => {
  it('should create health checker instance', () => {
    const mockConnectionPool = {} as ConnectionPool;
    const healthChecker = createHealthChecker(mockConnectionPool);

    expect(healthChecker).toBeInstanceOf(HealthChecker);
  });

  it('should create health checker with options', () => {
    const mockConnectionPool = {} as ConnectionPool;
    const options = {
      memoryThreshold: 512,
    };

    const healthChecker = createHealthChecker(mockConnectionPool, options);

    expect(healthChecker).toBeInstanceOf(HealthChecker);
  });
});
