import {
  RabbitMQ<PERSON><PERSON>,
  createMessage,
  createFun<PERSON><PERSON><PERSON><PERSON>,
  createTyped<PERSON><PERSON><PERSON>,
  createWorkQueue,
  createPubSub,
  createRPC,
  createLoggingMiddleware,
  createMetricsMiddleware,
  ValidationPatterns,
  globalValidator,
} from '../../src';
import { z } from 'zod';

// Skip integration tests if RabbitMQ is not available
const RABBITMQ_AVAILABLE = process.env.RABBITMQ_HOST || process.env.CI !== 'true';

describe('Full Stack Integration Tests', () => {
  let client: RabbitMQClient;

  beforeAll(async () => {
    if (!RABBITMQ_AVAILABLE) {
      return;
    }

    client = RabbitMQClient.create({
      host: process.env.RABBITMQ_HOST || 'localhost',
      port: parseInt(process.env.RABBITMQ_PORT || '5672'),
      username: process.env.RABBITMQ_USERNAME || 'guest',
      password: process.env.RABBITMQ_PASSWORD || 'guest',
    });

    await client.initialize();
  });

  afterAll(async () => {
    if (client) {
      await client.close();
    }
  });

  describe('Work Queue Pattern', () => {
    it('should process tasks with multiple workers', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      const workQueue = client.createWorkQueue('test_work_queue');
      await workQueue.setupQueue();

      const processedTasks: any[] = [];
      const handler = createFunctionHandler(async (taskData: any) => {
        processedTasks.push(taskData);
        return { processed: true, taskId: taskData.id };
      });

      // Start workers
      await workQueue.startWorkers(handler, 2, { prefetchCount: 1 });

      // Send tasks
      const tasks = [
        { id: 1, type: 'email', to: '<EMAIL>' },
        { id: 2, type: 'email', to: '<EMAIL>' },
        { id: 3, type: 'email', to: '<EMAIL>' },
      ];

      for (const task of tasks) {
        await workQueue.sendTask(task);
      }

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      expect(processedTasks).toHaveLength(3);
      expect(processedTasks.map(t => t.id).sort()).toEqual([1, 2, 3]);

      await workQueue.stopWorkers();
    }, 10000);

    it('should handle task failures and retries', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      const workQueue = client.createWorkQueue('test_retry_queue');
      await workQueue.setupQueue();

      let attempts = 0;
      const handler = createFunctionHandler(async (taskData: any) => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Simulated failure');
        }
        return { processed: true, attempts };
      });

      await workQueue.startWorkers(handler, 1);
      await workQueue.sendTask({ id: 'retry_test' });

      // Wait for retries
      await new Promise(resolve => setTimeout(resolve, 5000));

      expect(attempts).toBeGreaterThanOrEqual(3);

      await workQueue.stopWorkers();
    }, 15000);
  });

  describe('Pub/Sub Pattern', () => {
    it('should broadcast messages to multiple subscribers', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      const pubsub = client.createPubSub('test_pubsub');
      await pubsub.setupExchange();

      const subscriber1Messages: any[] = [];
      const subscriber2Messages: any[] = [];

      const handler1 = createFunctionHandler(async (data: any) => {
        subscriber1Messages.push(data);
        return { received: true };
      });

      const handler2 = createFunctionHandler(async (data: any) => {
        subscriber2Messages.push(data);
        return { received: true };
      });

      // Subscribe
      await pubsub.subscribe(handler1, { queueName: 'subscriber1' });
      await pubsub.subscribe(handler2, { queueName: 'subscriber2' });

      // Publish messages
      const messages = [
        { event: 'user_registered', userId: 1 },
        { event: 'user_login', userId: 1 },
        { event: 'user_logout', userId: 1 },
      ];

      for (const message of messages) {
        await pubsub.publish(message);
      }

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      expect(subscriber1Messages).toHaveLength(3);
      expect(subscriber2Messages).toHaveLength(3);
      expect(subscriber1Messages[0].event).toBe('user_registered');
      expect(subscriber2Messages[0].event).toBe('user_registered');

      await pubsub.stopAllSubscribers();
    }, 10000);
  });

  describe('RPC Pattern', () => {
    it('should handle request/response communication', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      const rpc = client.createRPC('test_calculator');

      // Setup server
      await rpc.setupServer({
        add: async (params: { a: number; b: number }) => {
          return params.a + params.b;
        },
        multiply: async (params: { a: number; b: number }) => {
          return params.a * params.b;
        },
        divide: async (params: { a: number; b: number }) => {
          if (params.b === 0) {
            throw new Error('Division by zero');
          }
          return params.a / params.b;
        },
      });

      // Setup client
      await rpc.setupClient();

      // Test successful calls
      const addResult = await rpc.call('add', { a: 5, b: 3 });
      expect(addResult).toBe(8);

      const multiplyResult = await rpc.call('multiply', { a: 4, b: 7 });
      expect(multiplyResult).toBe(28);

      // Test error handling
      await expect(rpc.call('divide', { a: 10, b: 0 })).rejects.toThrow('Division by zero');

      await rpc.close();
    }, 10000);
  });

  describe('Type Safety and Validation', () => {
    it('should validate messages using schemas', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      const UserEventSchema = z.object({
        userId: z.string(),
        eventType: z.enum(['login', 'logout', 'signup']),
        timestamp: z.string(),
      });

      const workQueue = client.createWorkQueue('test_validation_queue');
      await workQueue.setupQueue();

      const processedEvents: any[] = [];
      const handler = createTypedHandler(
        UserEventSchema,
        async (event) => {
          processedEvents.push(event);
          return { processed: true };
        }
      );

      await workQueue.startWorkers(handler, 1);

      // Send valid event
      await workQueue.sendTask({
        userId: 'user123',
        eventType: 'login',
        timestamp: new Date().toISOString(),
      });

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 1000));

      expect(processedEvents).toHaveLength(1);
      expect(processedEvents[0].userId).toBe('user123');

      await workQueue.stopWorkers();
    }, 10000);
  });

  describe('Middleware Integration', () => {
    it('should apply middleware to message processing', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      // Setup middleware
      const pipeline = client.getMiddlewarePipeline();
      const metricsMiddleware = createMetricsMiddleware();
      pipeline.add(createLoggingMiddleware({ logLevel: 'debug' }));
      pipeline.add(metricsMiddleware);

      const workQueue = client.createWorkQueue('test_middleware_queue');
      await workQueue.setupQueue();

      const handler = createFunctionHandler(async (data: any) => {
        return { processed: true, id: data.id };
      });

      await workQueue.startWorkers(handler, 1);

      // Send messages
      for (let i = 1; i <= 5; i++) {
        await workQueue.sendTask({ id: i, data: `test_${i}` });
      }

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Check metrics
      const metrics = metricsMiddleware.getMetrics();
      expect(metrics.sentCount).toBeGreaterThan(0);
      expect(metrics.processedCount).toBeGreaterThan(0);

      await workQueue.stopWorkers();
    }, 10000);
  });

  describe('Error Handling and Recovery', () => {
    it('should handle connection failures gracefully', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      const workQueue = client.createWorkQueue('test_error_queue');
      await workQueue.setupQueue();

      let errorCount = 0;
      const handler = createFunctionHandler(async (data: any) => {
        if (data.shouldFail) {
          errorCount++;
          throw new Error('Intentional failure');
        }
        return { processed: true };
      });

      await workQueue.startWorkers(handler, 1);

      // Send failing and successful messages
      await workQueue.sendTask({ id: 1, shouldFail: true });
      await workQueue.sendTask({ id: 2, shouldFail: false });
      await workQueue.sendTask({ id: 3, shouldFail: true });

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 3000));

      expect(errorCount).toBeGreaterThan(0);

      await workQueue.stopWorkers();
    }, 10000);
  });

  describe('Performance and Scalability', () => {
    it('should handle high message throughput', async () => {
      if (!RABBITMQ_AVAILABLE) {
        return;
      }

      const workQueue = client.createWorkQueue('test_performance_queue');
      await workQueue.setupQueue();

      let processedCount = 0;
      const handler = createFunctionHandler(async (data: any) => {
        processedCount++;
        return { processed: true };
      });

      // Start multiple workers
      await workQueue.startWorkers(handler, 5, { prefetchCount: 10 });

      // Send many messages
      const messageCount = 100;
      const startTime = Date.now();

      for (let i = 1; i <= messageCount; i++) {
        await workQueue.sendTask({ id: i, timestamp: Date.now() });
      }

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 10000));

      const endTime = Date.now();
      const duration = endTime - startTime;
      const throughput = processedCount / (duration / 1000);

      console.log(`Processed ${processedCount}/${messageCount} messages in ${duration}ms`);
      console.log(`Throughput: ${throughput.toFixed(2)} messages/second`);

      expect(processedCount).toBeGreaterThan(messageCount * 0.8); // At least 80% processed
      expect(throughput).toBeGreaterThan(5); // At least 5 messages/second

      await workQueue.stopWorkers();
    }, 30000);
  });
});

// Helper function to check if RabbitMQ is available
async function isRabbitMQAvailable(): Promise<boolean> {
  if (!RABBITMQ_AVAILABLE) {
    return false;
  }

  try {
    const testClient = RabbitMQClient.create({
      host: process.env.RABBITMQ_HOST || 'localhost',
      port: parseInt(process.env.RABBITMQ_PORT || '5672'),
      username: process.env.RABBITMQ_USERNAME || 'guest',
      password: process.env.RABBITMQ_PASSWORD || 'guest',
    });

    await testClient.initialize();
    await testClient.close();
    return true;
  } catch {
    return false;
  }
}
