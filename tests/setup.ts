import { LoggerFactory } from '../src/utils/logger';

// Set up silent logging for tests
LoggerFactory.setDefaultType('silent');

// Global test timeout
jest.setTimeout(30000);

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.RABBITMQ_HOST = 'localhost';
process.env.RABBITMQ_PORT = '5672';
process.env.RABBITMQ_USERNAME = 'guest';
process.env.RABBITMQ_PASSWORD = 'guest';

// Global test utilities
global.sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Extend Jest matchers
declare global {
  function sleep(ms: number): Promise<void>;
}
