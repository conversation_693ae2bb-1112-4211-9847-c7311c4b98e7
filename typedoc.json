{"entryPoints": ["src/index.ts"], "out": "docs/api", "theme": "default", "name": "@roasmax/rabbitmq", "includeVersion": true, "excludePrivate": true, "excludeProtected": false, "excludeExternals": true, "readme": "README.md", "categorizeByGroup": true, "categoryOrder": ["Core", "Patterns", "Middleware", "Performance", "Utilities", "*"], "sort": ["source-order"], "kindSortOrder": ["Class", "Interface", "Type alias", "Function", "Variable"], "navigation": {"includeCategories": true, "includeGroups": true}, "plugin": ["typedoc-plugin-markdown"], "gitRevision": "main", "gitRemote": "origin"}